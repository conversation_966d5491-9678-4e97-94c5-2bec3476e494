# 网关配置
GATEWAY_ADDR=:8080

# JWT配置
JWT_SECRET=mini-douyin-2024-secret-key
PASSWORD_SALT=mini-douyin-salt-123

# Consul配置
CONSUL_ADDR=localhost:8500
RPC_PORT=50051

# PostgreSQL配置（与docker-compose一致）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=douyin
POSTGRES_PASSWORD=douyin
POSTGRES_DB=douyin

# Redis配置
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# RabbitMQ配置（与docker-compose一致）
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=root
RABBITMQ_PASSWORD=123456
RABBITMQ_VHOST=/

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_VIDEO_BUCKET=videos
MINIO_IMAGE_BUCKET=images

# Jaeger配置
JAEGER_ENDPOINT=http://localhost:14268/api/traces