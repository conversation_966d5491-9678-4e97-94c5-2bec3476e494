# docker-compose.yml
version: '3.8'
services:
  consul:
    image: consul:1.15
    ports:
      - "8500:8500"
    command: agent -dev -client=0.0.0.0

  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER: douyin
      POSTGRES_PASSWORD: douyin
      POSTGRES_DB: douyin
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: root
      RABBITMQ_DEFAULT_PASS: 123456

  jaeger:
    image: jaegertracing/all-in-one:1.47
    ports:
      - "6831:6831/udp"
      - "16686:16686"

volumes:
  pgdata: